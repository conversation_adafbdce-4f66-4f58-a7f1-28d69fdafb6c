<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.FinanceCollectionMapper">


    <select id="getCurrMonthFinanceCollection" resultType="com.hightop.benyin.statistics.infrastructure.entity.FinanceCollection">
       select t.customer_id,t1.seq_id customerCode,t1.name customerName,t1.industry_attr,
        t2.name area,t3.name city,t4.full_name province,#{qo.yearMonths} yearMonths,
        sum(amount) terminalAmount,
        ifnull(tc.period_adjust,0) periodAdjust,
        ifnull(tt.terminal_amount,0) periodAmount from (
            select t.customer_id,  sum(t.actual_amount) amount
            from tb_trade_order t where t.order_status != 'CLOSED'and t.pay_mode = 'CYCLE' and t.order_type = 'SALE'
            <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                and  date_format(t.created_at,'%Y-%m')&lt;= #{qo.yearMonths}
            </if>
            group by customer_id
        union all
            select t.customer_id,sum(amount) amount
            from (
                select distinct t.id,t.customer_id , t.arrears_amount amount
                from b_customer_contract t
                where t.arrears_amount > 0
                and t.arrears_pay_status = 0
                and t.is_supplement = 0
                <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                    and  date_format(t.created_at,'%Y-%m')&lt;= #{qo.yearMonths}
                </if>
                union all
                select distinct t.id,t.customer_id,  t.amount
                from tb_trade_order_installment t
                left join b_customer_contract tt on tt.code = t.contract_code
                <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                    and date_format(t.plan_pay_date,'%Y-%m')&lt;= #{qo.yearMonths}
                </if>
                where t.status not in ('CANCEL','PAID') and tt.status!='CANCEL'
            ) t
            group by t.customer_id
        union all
            select t.customer_id, sum( t.amount * 100) amount
            from b_iot_print_count t
            left join b_iot_print_receipt tt on tt.code = t.receipt_code
            where ((tt.status!='604' and tt.amount>0) or t.settment_status=0) and t.amount>0
            <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                and t.cycle &lt; #{qo.yearMonths}
            </if>
            group by t.customer_id
        union all
            select  t.customer_id, sum(t.total_pay) amount
            from tb_work_order t
            where t.status not in ('completed', 'close', 'pending_orders')
            and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
            <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                and date_format(t.created_at,'%Y-%m')&lt;= #{qo.yearMonths}
            </if>
            group by t.customer_id
        ) t
        left join r_finance_collection tt on tt.customer_id = t.customer_id and tt.year_months=#{qo.lastMonths}
        left join r_finance_collection tc on tc.customer_id = t.customer_id and tc.year_months=#{qo.yearMonths}
        left join b_customer t1 on t1.id = t.customer_id
        left join b_customer_business t5 on t5.customer_id = t1.id
        left join b_region t2 on t2.code = t1.region_code
        left join b_region t3 on t3.code = t2.parent_code
        left join b_region t4 on t4.code = t3.parent_code
        where 1=1
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t1.seq_id like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.industryAttr and '' != qo.industryAttr ">
            and t1.industry_attr =#{qo.industryAttr}
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t5.license like concat ('%',#{qo.license},'%')
        </if>
        group by t.customer_id,t1.seq_id,t1.name,t1.industry_attr, t2.name,t3.name,t4.name
    </select>

    <select id="getCurrMonthPayCollection" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.customer_id, sum(amount) amount
        from (select t.customer_id, sum(t.actual_amount) amount
            from tb_trade_order t
            left join b_pay_order t1 on t1.trade_order_number = t.order_num
            where t1.status = 'PAID_SUCCESS'
            and t.is_cycle = '1'
            and date_format(t1.created_at, '%Y-%m') =#{yearMonths}
            and date_format(t.created_at, '%Y-%m') &lt;#{yearMonths}
            <if test="null!=customerIds and !customerIds.isEmpty()">
                AND t.customer_id in
                <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            group by customer_id
        union all
            select t.customer_id, sum(amount) amount
                from (select distinct t.id,t.customer_id, t.arrears_amount amount
                from b_customer_contract t
                left join b_pay_order t1 on t1.trade_order_number = t.code
                where t.arrears_amount > 0
                and t.is_supplement = 0
                and t1.trade_order_origin = 'CONTRACT_ARR'
                and t1.status = 'PAID_SUCCESS'
                and date_format(t.sign_time, '%Y-%m') &lt; #{yearMonths}
                and date_format(t1.created_at, '%Y-%m') = #{yearMonths}
                <if test="null!=customerIds and !customerIds.isEmpty()">
                    AND t.customer_id in
                    <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </if>
            union all
                select distinct t.id,t.customer_id, t.amount
                from tb_trade_order_installment t
                left join b_pay_order t1 on t1.trade_order_number = t.code
                where t1.status = 'PAID_SUCCESS'
                and t1.trade_order_origin='INSTALLMENT'
                and date_format(t.plan_pay_date, '%Y-%m') &lt; #{yearMonths}
                and date_format(t1.created_at, '%Y-%m') = #{yearMonths}
                <if test="null!=customerIds and !customerIds.isEmpty()">
                    AND t.customer_id in
                    <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </if>
            ) t
            group by t.customer_id
        union all
            select t2.customer_id, sum(t2.amount) amount
            from b_iot_print_receipt t
            join b_pay_order t1 on t1.trade_order_number = t.code
            join b_iot_print_count t2 on t2.receipt_code = t.code
            where t1.status = 'PAID_SUCCESS'
            and t1.trade_order_origin='RECEIPT_ORDER'
            and t2.cycle&lt; #{lastMonths}
            and date_format(t1.created_at, '%Y-%m') = #{yearMonths}
            <if test="null!=customerIds and !customerIds.isEmpty()">
                AND t.customer_id in
                <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            group by t.customer_id
        union all
            select t.customer_id, sum(t.total_pay) amount
            from tb_work_order t
            join b_pay_order t1 on t1.trade_order_number = t.code
            where t.status not in ('completed', 'close', 'pending_orders')
            and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
            and date_format(t.created_at, '%Y-%m') &lt; #{yearMonths}
            and date_format(t1.created_at, '%Y-%m') = #{yearMonths}
            <if test="null!=customerIds and !customerIds.isEmpty()">
                AND t.customer_id in
                <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            group by t.customer_id
            ) t
        group by t.customer_id
    </select>
    <select id="getCurrMonthNewCollection" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.customer_id,sum(amount) amount from (
        select t.customer_id,  sum(t.actual_amount) amount
        from tb_trade_order t where t.order_status != 'CLOSED'and t.pay_mode = 'CYCLE' and t.order_type = 'SALE'
        <if test="null != yearMonths and '' != yearMonths ">
            and  date_format(t.created_at,'%Y-%m')= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by customer_id
        union all
        select t.customer_id,sum(amount) amount
        from (
        select t.customer_id , t.arrears_amount amount
        from b_customer_contract t
        where t.arrears_amount > 0
        and t.arrears_pay_status = 0
        and t.is_supplement = 0
        and t.status not in ('CANCEL','UNEFFECT')
        <if test="null != yearMonths and '' != yearMonths ">
            and  date_format(t.sign_time,'%Y-%m')= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        union all
        select t.customer_id,  t.amount
        from tb_trade_order_installment t
        left join b_customer_contract tt on tt.code = t.contract_code
        where t.status not in ('CANCEL','PAID') and tt.status not in ('CANCEL','UNEFFECT')
        <if test="null != yearMonths and '' != yearMonths ">
            and date_format(t.plan_pay_date,'%Y-%m')= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        ) t
        group by t.customer_id
        union all
        select t.customer_id, sum( t.amount * 100) amount
        from b_iot_print_count t
        left join b_iot_print_receipt tt on tt.code = t.receipt_code
        where ((tt.status!='604' and tt.amount>0) or t.settment_status=0) and t.amount>0
        <if test="null != yearMonths and '' != yearMonths ">
            and date_format(tt.created_at,'%Y-%m')= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t.customer_id
        union all
        select t.customer_id, sum(t.total_pay) amount
        from tb_work_order t
        where t.status not in ('completed', 'close', 'pending_orders')
        and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
        <if test="null != yearMonths and '' != yearMonths ">
            and date_format(t.created_at,'%Y-%m')= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t.customer_id
        ) t
        group by t.customer_id
    </select>

    <select id="getFinanceCollection" resultType="com.hightop.benyin.statistics.infrastructure.entity.FinanceCollection">
        select t.*,t1.seq_id customerCode,t1.name customerName,t1.industry_attr,
               t2.name area,t3.name city,t4.full_name province,t5.license license from r_finance_collection t
                          left join b_customer t1 on t1.id = t.customer_id
                          left join b_customer_business t5 on t5.customer_id = t1.id
                          left join b_region t2 on t2.code = t1.region_code
                          left join b_region t3 on t3.code = t2.parent_code
                          left join b_region t4 on t4.code = t3.parent_code
        where 1=1
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t1.seq_id like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.industryAttr and '' != qo.industryAttr ">
            and t1.industry_attr =#{qo.industryAttr}
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t5.license like concat ('%',#{qo.license},'%')
        </if>
        <if test="null != qo.yearMonths and '' != qo.yearMonths ">
            and t.year_months = #{qo.yearMonths}
        </if>
        order by t.year_months,t.id desc
    </select>

    <select id="materialCollectionList" resultType="com.hightop.benyin.statistics.application.vo.MaterialCollectionVO">
        SELECT
        tem.id,
        tem.CODE,
        tem.STATUS,
        tem.customerCode,
        tem.customerName,
        tem.customerId,
        tem.`NAME`,
        tem.manufacturer_channel,
        tem.license,
        SUM(tem.price) price,
        SUM(tem.num) num,
        SUM(tem.amount) amount,
        tem.shippingFee,
        tem.orderAmount,
        tem.logistics_provider,
        tem.created_at
        FROM
        ( select distinct
        tt.id,
        t.order_num code,
        t.order_status status,
        t1.seq_id customerCode,
        t1.name customerName,
        t1.id customerId,
        t2.name,
        ta.code articleCode,
        ta.name articleName,
        ta.manufacturer_channel,
        ta.unit,
        t3.license license,
        round(tt.actual_unit_price/100,2) price,
        tt.item_num num,
        round(tt.actual_pay_amount/100,2) amount,
        round( t.shipping_fee/100,2) shippingFee,
        round( t.actual_amount/100 ,2) orderAmount,
        t.logistics_provider,
        t.created_at
        from tb_trade_order_detail tt
        left join b_storage_article ta on ta.code = tt.article_code
        left join tb_trade_order t on tt.trade_order_id = t.id
        left join b_customer t1 on t.customer_id = t1.id
        left join b_region t2 on t1.region_code = t2.code
        left join b_customer_business t3 on t3.customer_id = t1.id
        where t.order_status != 'CLOSED'
        and t.pay_mode = 'CYCLE'
        and t.order_type = 'SALE'
        <if test="null != qo.code and '' != qo.code ">
            and t.order_num like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and tt.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and ta.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t1.seq_id like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t3.license like concat ('%',#{qo.license},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.status and '' != qo.status ">
            and t.order_status=#{qo.status}
        </if>
        order by t.created_at desc, t.order_num, tt.article_code desc
        ) tem
        GROUP BY
        tem.CODE
        ORDER BY
        tem.created_at DESC
    </select>

    <select id="machineCollectionList" resultType="com.hightop.benyin.statistics.application.vo.MachineCollectionVO">
        select * from (
        <!-- 定金 + 全款也是定金中的一种 +分期定金（如果有定金的话）-->
        select
        t.id,
        '' installmentCode,
        2 type,
        t.code contractCode,
        t.id contractId,
        t.contract_name,
        t1.seq_id        customerCode,
        t1.name          customerName,
        t1.id customerId,
        t4.license license,
        t.contract_type,
        t.sign_time,
        ''               productName,
        ''               machineNum,
        round(t.total_amount/100,2) price,
        round(t.prepayment/100,2)     depositAmount,
        t.created_at
        from b_customer_contract t
        left join b_customer t1 on t1.id = t.customer_id
        left join b_customer_business t4 on t4.customer_id = t1.id
        where t.prepayment > 0
        and t.pre_pay_status = 0
        and t.is_supplement = 0
        and t.status not in ('CANCEL','UNEFFECT')
        union all
        select
        t.id,
        '' installmentCode,
        0 type,
        t.code contractCode,
        t.id contractId,
        t.contract_name,
        t1.seq_id        customerCode,
        t1.name          customerName,
        t1.id customerId,
        t4.license license,
        t.contract_type,
        t.sign_time,
        ''               productName,
        ''               machineNum,
        round(t.total_amount/100,2) price,
        round(t.arrears_amount/100,2)     depositAmount,
        t.created_at
        from b_customer_contract t
        left join b_customer t1 on t1.id = t.customer_id
        left join b_customer_business t4 on t4.customer_id = t1.id
        where t.arrears_amount > 0
        and t.arrears_pay_status = 0
        and t.is_supplement = 0
        and t.status not in ('CANCEL','UNEFFECT')
        union all
        select t.id,
        t.code installmentCode,
        1 type,
        t.contract_code contractCode,
        tt.id contractId,
        tt.contract_name,
        t1.seq_id        customerCode,
        t1.name          customerName,
        t1.id customerId,
        t4.license license,
        tt.contract_type,
        tt.sign_time,
        t3.name productName,
        t2.machine_num              machineNum,
        round(t2.full_amount/100,2) price,
        round(t.amount/100,2)     depositAmount,
        t.created_at
        from tb_trade_order_installment t
        left join b_customer_contract tt on tt.code = t.contract_code
        left join b_customer t1 on t1.id = t.customer_id
        left join b_customer_business t4 on t4.customer_id = t1.id
        left join b_customer_contract_buy t2 on t2.code = t.item_code
        left join b_product_tree t3 on t3.id = t2.product_id
        where
        date_format(t.plan_pay_date, '%Y-%m') &lt;=date_format(current_date, '%Y-%m') and t.status not in ('CANCEL','PAID') and tt.status not in ('CANCEL','UNEFFECT') and tt.contract_type='1201'
        union all
        select t.id,
        t.code installmentCode,
        1 type,
        t.contract_code contractCode,
        tt.id contractId,
        tt.contract_name,
        t1.seq_id        customerCode,
        t1.name          customerName,
        t1.id customerId,
        t4.license license,
        tt.contract_type,
        tt.sign_time,
        t3.name productName,
        t2.machine_num              machineNum,
        round((t2.package_amount-t2.package_pre_amount)/100,2) amount,
        round(t.amount/100,2)   depositAmount,
        t.created_at
        from tb_trade_order_installment t
        left join b_customer_contract tt on tt.code = t.contract_code
        left join b_customer t1 on t1.id = t.customer_id
        left join b_customer_business t4 on t4.customer_id = t1.id
        left join b_customer_contract_serve t2 on t2.code = t.item_code
        left join b_product_tree t3 on t3.id = t2.product_id
        where date_format(t.plan_pay_date, '%Y-%m') &lt;=date_format(current_date, '%Y-%m') and t.status not in ('CANCEL','PAID') and tt.status not in ('CANCEL','UNEFFECT') and tt.contract_type!='1201'
        ) t
        where 1=1
        <if test="null != qo.contractCode and '' != qo.contractCode ">
            and t.contractCode like concat ('%',#{qo.contractCode},'%')
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type=#{qo.type}
        </if>

        <if test="null != qo.contractName and '' != qo.contractName ">
            and t.contract_name=#{qo.contractName}
        </if>

        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machineNum like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t.customerCode like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.customerName like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t.license like concat ('%',#{qo.license},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        order by t.created_at desc
    </select>

    <select id="operationCollectionList" resultType="com.hightop.benyin.statistics.application.vo.OperationCollectionVO">
        SELECT
        tem.id,
        tem.receipt_code,
        tem.customerId,
        tem.customerCode,
        tem.customerName,
        tem.license,
        tem.ser_type,
        SUM(amount) amount,
        SUM(derateAmount) derateAmount,
        SUM(total_amount) total_amount
        FROM
        (
        select distinct
            tt.id,
            t.receipt_code,
            <!--t2.contract_code,
            t3.contract_name,-->
               t1.id customerId,
               t1.seq_id        customerCode,
               t1.name          customerName,
               t4.license,
               t2.ser_type,
        <!--t.device_group,
        t2.start_time startDate,
        t2.end_time endDate,
        t.machine productName,
        t2.machine_num,-->
               t2.waste_type,
               t.created_at,
               t.amount,
               t.derate_amount derateAmount,
               tt.total_amount
        from b_iot_print_count t
                 left join b_iot_print_receipt tt on tt.code = t.receipt_code
                 left join b_customer t1 on t.customer_id = t1.id
                 left join b_customer_contract_serve t2 on t2.code=t.contract_item_code
                 left join b_customer_contract t3 on t3.code=t2.contract_code
                 left join b_customer_business t4 on t4.customer_id = t1.id
        where   ((tt.status!='604' and tt.amount>0) or t.settment_status=0) and t.amount>0

        <if test="null!=qo.serType and !qo.serType.isEmpty()">
            AND t2.ser_type in
            <foreach collection="qo.serType" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.receiptCode and '' != qo.receiptCode ">
            and t.receipt_code like concat ('%',#{qo.receiptCode},'%')
        </if>
        <if test="null != qo.contractCode and '' != qo.contractCode ">
            and t2.contract_code like concat ('%',#{qo.contractCode},'%')
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t2.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t1.seq_id like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t4.license like concat ('%',#{qo.license},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        order by t.receipt_code,t1.seq_id desc
        ) tem
        GROUP BY
        tem.receipt_code
    </select>

    <select id="repairCollectionList" resultType="com.hightop.benyin.statistics.application.vo.RepairCollectionVO">
        select distinct t.code,
        t1.seq_id customerCode,
        t1.name customerName,
        t1.id customerId,
        t6.license,
        t2.name,
        t3.device_seq_id deviceSeqId,
        t3.device_group,
        t4.name productName,
        t.machine_num,
        t5.name enginnerName,
        round((t.total_amount - t.item_pay)/100,2) totalAmount,
        round( (t.total_pay - t.item_pay)/100,2) totalPay,
        round(t.discount_amount/100,2) discountAmount,
        t.status,
        t.ser_type,
        t.created_at
        from tb_work_order t
        left join b_customer t1 on t.customer_id = t1.id
        left join b_region t2 on t1.region_code = t2.code
        left join b_customer_device_group t3 on t3.id = t.device_group_id
        left join b_product_tree t4 on t4.id = t.product_id
        left join st_user_basic t5 on t5.id = t.engineer_id
        left join b_customer_business t6 on t6.customer_id = t1.id
        where t.status not in ('completed', 'close', 'pending_orders')
        and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
        and round( (t.total_pay - t.item_pay)/100,2) > 0

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.deviceSeqId and '' != qo.deviceSeqId ">
            and t3.device_seq_id like concat ('%',#{qo.deviceSeqId},'%')
        </if>
        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t1.seq_id like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t6.license like concat ('%',#{qo.license},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        order by t.code desc
    </select>

    <select id="getCustomerOrderSummary" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.customer_id,  sum(t.actual_amount) amount
        from tb_trade_order t where t.order_status != 'CLOSED'and t.pay_mode = 'CYCLE' and t.order_type = 'SALE'
            <if test="null != yearMonths and '' != yearMonths ">
               and date_format(t.created_at,'%Y-%m')&lt;= #{yearMonths}
            </if>
            <if test="null!=customerIds and !customerIds.isEmpty()">
                AND t.customer_id in
                <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        group by customer_id
    </select>

    <select id="getCustomerMachineSummary" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.customer_id,sum(amount) amount
        from (select distinct t.id,t.customer_id , t.arrears_amount amount
        from b_customer_contract t
        where t.arrears_amount > 0
        and t.arrears_pay_status = 0
        and t.is_supplement = 0
        and t.status not in ('CANCEL','UNEFFECT')
        <if test="null != yearMonths and '' != yearMonths ">
           and  date_format(t.sign_time,'%Y-%m')&lt;= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        union all
        select distinct t.id,t.customer_id,  t.amount
        from tb_trade_order_installment t
        left join b_customer_contract tt on tt.code = t.contract_code
        where t.status not in ('CANCEL','PAID') and tt.status NOT IN('CANCEL','UNEFFECT')
        <if test="null != yearMonths and '' != yearMonths ">
            and date_format(t.plan_pay_date,'%Y-%m')&lt;= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        ) t
        group by t.customer_id
    </select>

    <select id="getCustomerOperationSummary" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.customer_id, sum( t.amount * 100) amount
        from b_iot_print_count t
                 left join b_iot_print_receipt tt on tt.code = t.receipt_code
        where ((tt.status!='604' and tt.amount>0) or t.settment_status=0) and t.amount>0
        <if test="null != yearMonths and '' != yearMonths ">
            and t.cycle &lt; #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t.customer_id
    </select>

    <select id="getCustomerRepairSummary" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.customer_id, sum(t.total_pay) amount
        from tb_work_order t
        where t.status not in ('completed', 'close', 'pending_orders')
          and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
        <if test="null != yearMonths and '' != yearMonths ">
            and date_format(t.created_at,'%Y-%m')&lt;= #{yearMonths}
        </if>
        <if test="null!=customerIds and !customerIds.isEmpty()">
            AND t.customer_id in
            <foreach collection="customerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t.customer_id
    </select>
</mapper>

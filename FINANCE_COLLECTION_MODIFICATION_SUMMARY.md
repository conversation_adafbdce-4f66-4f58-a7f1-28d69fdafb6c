# 财务应收账款修改总结

## 修改目标
将维修工单中的换件费用从维修费用中分离出来，算到耗材应收中。修改后：
- **维修费用**：只包含人工费用
- **耗材应收**：包含原有的销售订单金额 + 维修工单的换件和耗材费用

## 修改内容

### 1. getCustomerRepairSummary 方法修改
**文件位置**: `src/main/resources/mapper/FinanceCollectionMapper.xml`

**修改前**:
```sql
select t.customer_id, sum(t.total_pay) amount
from tb_work_order t
where t.status not in ('completed', 'close', 'pending_orders')
  and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
```

**修改后**:
```sql
select t.customer_id, sum(
    COALESCE(t.repair_pay, 0) + 
    COALESCE(t.visit_pay, 0) + 
    COALESCE(t.additional_pay, 0) + 
    COALESCE(t.engineer_additional_pay, 0) + 
    COALESCE(t.long_way_visit_pay, 0) - 
    COALESCE(t.derate_amount, 0)
) amount
from tb_work_order t
where t.status not in ('completed', 'close', 'pending_orders')
  and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
```

**说明**: 只统计人工相关费用，不包含 `item_pay`（耗材费）和 `replace_pay`（换件费）

### 2. getCustomerOrderSummary 方法修改
**文件位置**: `src/main/resources/mapper/FinanceCollectionMapper.xml`

**修改前**:
```sql
select t.customer_id, sum(t.actual_amount) amount
from tb_trade_order t 
where t.order_status != 'CLOSED' and t.pay_mode = 'CYCLE' and t.order_type = 'SALE'
```

**修改后**:
```sql
select customer_id, sum(amount) amount from (
    -- 原有的销售订单
    select t.customer_id, sum(t.actual_amount) amount
    from tb_trade_order t 
    where t.order_status != 'CLOSED' and t.pay_mode = 'CYCLE' and t.order_type = 'SALE'
    group by t.customer_id
    
    UNION ALL
    
    -- 维修工单的换件和耗材费用
    select t.customer_id, sum(COALESCE(t.item_pay, 0) + COALESCE(t.replace_pay, 0)) amount
    from tb_work_order t
    where t.status not in ('completed', 'close', 'pending_orders')
      and t.ser_type in ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER')
      and (COALESCE(t.item_pay, 0) + COALESCE(t.replace_pay, 0)) > 0
    group by t.customer_id
) combined
group by customer_id
```

**说明**: 使用 UNION ALL 将销售订单金额和维修工单的换件耗材费用合并统计

## 费用字段说明

### WorkOrder 表相关字段
- `total_pay`: 总费用（修改前包含所有费用）
- `repair_pay`: 维修费
- `visit_pay`: 上门费
- `replace_pay`: 换件费
- `item_pay`: 耗材费
- `additional_pay`: 客户追加费用
- `engineer_additional_pay`: 工程师追加费用
- `long_way_visit_pay`: 远途费
- `derate_amount`: 减免费用

### 费用分类逻辑
**人工费用** (getCustomerRepairSummary):
- repair_pay + visit_pay + additional_pay + engineer_additional_pay + long_way_visit_pay - derate_amount

**换件耗材费用** (getCustomerOrderSummary 新增部分):
- item_pay + replace_pay

## 测试验证
创建了测试文件 `src/test/java/com/hightop/benyin/statistics/FinanceCollectionMapperTest.java` 用于验证修改效果。

## 影响范围
1. **直接影响**: `getCurrMonthFinanceCollection` 方法中调用的两个汇总方法
2. **业务影响**: 财务应收账款统计报表中的维修费用和耗材费用分类
3. **数据一致性**: 修改后总金额保持不变，只是重新分类

## 注意事项
1. 修改保持了原有的查询条件和参数处理逻辑
2. 使用 COALESCE 函数处理 NULL 值，确保计算准确性
3. 在维修工单查询中增加了费用大于0的条件，避免无意义的记录
4. 保持了原有的时间范围和客户ID过滤条件

## 验证建议
1. 运行测试用例验证SQL语法正确性
2. 对比修改前后的数据总和，确保一致性
3. 检查业务报表显示是否符合预期
4. 验证边界情况（如费用为0或NULL的情况）
